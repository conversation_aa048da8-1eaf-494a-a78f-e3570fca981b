"""
Unit tests for Turkish team models.

These tests verify that Turkish team models work correctly and use the Turkish database.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from .models_tr import TeamMemberTR


class TeamMemberTRModelTest(TestCase):
    """Test TeamMemberTR model functionality."""
    databases = ['default', 'turkish']
    
    def setUp(self):
        """Set up test data."""
        self.team_member_data = {
            'full_name': 'Ahmet Yılmaz',
            'position': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Geliştirici',
            'bio': 'Python ve Django konularında uzman yazılım geliştirici. 5 yıllık deneyime sahip.',
            'email': '<EMAIL>',
            'linkedin_url': 'https://linkedin.com/in/ahmet-yilmaz',
            'github_url': 'https://github.com/ahmetyilmaz'
        }
    
    def test_create_team_member_tr(self):
        """Test creating TeamMemberTR instance."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        
        self.assertEqual(member.full_name, '<PERSON><PERSON>')
        self.assertEqual(member.position, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tirici')
        self.assertEqual(member.email, '<EMAIL>')
        self.assertTrue(member.is_active)
        self.assertEqual(member.display_order, 0)
        self.assertIsNotNone(member.created_at)
        self.assertIsNotNone(member.updated_at)
    
    def test_team_member_tr_uses_turkish_database(self):
        """Test that TeamMemberTR uses Turkish database."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        self.assertEqual(member._state.db, 'turkish')
    
    def test_team_member_tr_str_representation(self):
        """Test string representation of TeamMemberTR."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        expected_str = f"{member.full_name} - {member.position}"
        self.assertEqual(str(member), expected_str)
    
    def test_first_name_property(self):
        """Test first_name property."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        self.assertEqual(member.first_name, 'Ahmet')
        
        # Test with single name
        single_name_data = self.team_member_data.copy()
        single_name_data['full_name'] = 'Mehmet'
        single_name_data['email'] = '<EMAIL>'
        member2 = TeamMemberTR.objects.create(**single_name_data)
        self.assertEqual(member2.first_name, 'Mehmet')
    
    def test_last_name_property(self):
        """Test last_name property."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        self.assertEqual(member.last_name, 'Yılmaz')
        
        # Test with multiple last names
        multi_name_data = self.team_member_data.copy()
        multi_name_data['full_name'] = 'Ali Veli Kaya'
        multi_name_data['email'] = '<EMAIL>'
        member2 = TeamMemberTR.objects.create(**multi_name_data)
        self.assertEqual(member2.last_name, 'Veli Kaya')
        
        # Test with single name
        single_name_data = self.team_member_data.copy()
        single_name_data['full_name'] = 'Mehmet'
        single_name_data['email'] = '<EMAIL>'
        member3 = TeamMemberTR.objects.create(**single_name_data)
        self.assertEqual(member3.last_name, '')
    
    def test_initials_property(self):
        """Test initials property."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        self.assertEqual(member.initials, 'AY')
        
        # Test with single name
        single_name_data = self.team_member_data.copy()
        single_name_data['full_name'] = 'Mehmet'
        single_name_data['email'] = '<EMAIL>'
        member2 = TeamMemberTR.objects.create(**single_name_data)
        self.assertEqual(member2.initials, 'ME')
        
        # Test with empty name
        empty_name_data = self.team_member_data.copy()
        empty_name_data['full_name'] = ''
        empty_name_data['email'] = '<EMAIL>'
        member3 = TeamMemberTR.objects.create(**empty_name_data)
        self.assertEqual(member3.initials, 'TÜ')  # Turkish default
    
    def test_has_social_links_property(self):
        """Test has_social_links property."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        self.assertTrue(member.has_social_links)
        
        # Test with no social links
        no_social_data = self.team_member_data.copy()
        no_social_data['linkedin_url'] = ''
        no_social_data['github_url'] = ''
        no_social_data['email'] = '<EMAIL>'
        member2 = TeamMemberTR.objects.create(**no_social_data)
        self.assertFalse(member2.has_social_links)
    
    def test_get_social_links_method(self):
        """Test get_social_links method."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        social_links = member.get_social_links()
        
        expected_links = {
            'linkedin': 'https://linkedin.com/in/ahmet-yilmaz',
            'github': 'https://github.com/ahmetyilmaz'
        }
        self.assertEqual(social_links, expected_links)
        
        # Test with only one social link
        one_social_data = self.team_member_data.copy()
        one_social_data['github_url'] = ''
        one_social_data['email'] = '<EMAIL>'
        member2 = TeamMemberTR.objects.create(**one_social_data)
        social_links2 = member2.get_social_links()
        
        expected_links2 = {
            'linkedin': 'https://linkedin.com/in/ahmet-yilmaz'
        }
        self.assertEqual(social_links2, expected_links2)
    
    def test_contact_methods_property(self):
        """Test contact_methods property."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        contact_methods = member.contact_methods
        
        self.assertEqual(len(contact_methods), 3)  # email, linkedin, github
        
        # Check email method
        email_method = next((m for m in contact_methods if m['type'] == 'email'), None)
        self.assertIsNotNone(email_method)
        self.assertEqual(email_method['value'], '<EMAIL>')
        self.assertEqual(email_method['display'], 'E-posta')
        
        # Check LinkedIn method
        linkedin_method = next((m for m in contact_methods if m['type'] == 'linkedin'), None)
        self.assertIsNotNone(linkedin_method)
        self.assertEqual(linkedin_method['value'], 'https://linkedin.com/in/ahmet-yilmaz')
        self.assertEqual(linkedin_method['display'], 'LinkedIn')
    
    def test_short_bio_property(self):
        """Test short_bio property."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        short_bio = member.short_bio
        
        # Should return full bio if under 150 characters
        self.assertEqual(short_bio, member.bio)
        
        # Test with long bio
        long_bio_data = self.team_member_data.copy()
        long_bio_data['bio'] = 'Bu çok uzun bir biyografi metnidir. ' * 10  # Make it long
        long_bio_data['email'] = '<EMAIL>'
        member2 = TeamMemberTR.objects.create(**long_bio_data)
        short_bio2 = member2.short_bio
        
        self.assertTrue(len(short_bio2) <= 153)  # 150 + "..."
        self.assertTrue(short_bio2.endswith('...'))
        
        # Test with no bio
        no_bio_data = self.team_member_data.copy()
        no_bio_data['bio'] = ''
        no_bio_data['email'] = '<EMAIL>'
        member3 = TeamMemberTR.objects.create(**no_bio_data)
        self.assertEqual(member3.short_bio, '')
    
    def test_get_expertise_areas_method(self):
        """Test get_expertise_areas method."""
        # Test with bio containing expertise keywords
        expertise_data = self.team_member_data.copy()
        expertise_data['bio'] = 'Python, Django, React ve JavaScript konularında uzmanım. PostgreSQL veritabanı deneyimim var.'
        expertise_data['position'] = 'Full-stack Developer'
        expertise_data['email'] = '<EMAIL>'
        member = TeamMemberTR.objects.create(**expertise_data)
        
        expertise_areas = member.get_expertise_areas()
        
        # Should find Python, Django, React, JavaScript, PostgreSQL
        expected_areas = ['Python', 'Django', 'React', 'JavaScript', 'PostgreSQL']
        for area in expected_areas:
            self.assertIn(area, expertise_areas)
        
        # Should return max 5 areas
        self.assertLessEqual(len(expertise_areas), 5)
    
    def test_get_active_members_class_method(self):
        """Test get_active_members class method."""
        # Create active and inactive members
        active_member = TeamMemberTR.objects.create(**self.team_member_data)
        
        inactive_data = self.team_member_data.copy()
        inactive_data['full_name'] = 'Inactive Member'
        inactive_data['email'] = '<EMAIL>'
        inactive_data['is_active'] = False
        inactive_member = TeamMemberTR.objects.create(**inactive_data)
        
        active_members = TeamMemberTR.get_active_members()
        
        self.assertIn(active_member, active_members)
        self.assertNotIn(inactive_member, active_members)
    
    def test_get_featured_members_class_method(self):
        """Test get_featured_members class method."""
        # Create multiple members with different display orders
        member1_data = self.team_member_data.copy()
        member1_data['display_order'] = 1
        member1 = TeamMemberTR.objects.create(**member1_data)
        
        member2_data = self.team_member_data.copy()
        member2_data['full_name'] = 'Member 2'
        member2_data['email'] = '<EMAIL>'
        member2_data['display_order'] = 2
        member2 = TeamMemberTR.objects.create(**member2_data)
        
        member3_data = self.team_member_data.copy()
        member3_data['full_name'] = 'Member 3'
        member3_data['email'] = '<EMAIL>'
        member3_data['display_order'] = 3
        member3 = TeamMemberTR.objects.create(**member3_data)
        
        member4_data = self.team_member_data.copy()
        member4_data['full_name'] = 'Member 4'
        member4_data['email'] = '<EMAIL>'
        member4_data['display_order'] = 4
        member4 = TeamMemberTR.objects.create(**member4_data)
        
        featured_members = TeamMemberTR.get_featured_members(limit=3)
        
        self.assertEqual(len(featured_members), 3)
        self.assertEqual(featured_members[0], member1)
        self.assertEqual(featured_members[1], member2)
        self.assertEqual(featured_members[2], member3)
    
    def test_team_member_validation(self):
        """Test TeamMemberTR validation."""
        # Test that at least one contact method is required
        invalid_data = {
            'full_name': 'Test Member',
            'position': 'Test Position',
            'email': '',
            'linkedin_url': '',
            'github_url': ''
        }
        
        member = TeamMemberTR(**invalid_data)
        with self.assertRaises(ValidationError):
            member.full_clean()
    
    def test_turkish_model_manager(self):
        """Test that TeamMemberTR uses TurkishModelManager."""
        member = TeamMemberTR.objects.create(**self.team_member_data)
        
        # Test that queryset uses Turkish database
        queryset = TeamMemberTR.objects.all()
        self.assertEqual(queryset.db, 'turkish')
        
        # Test get_or_create uses Turkish database
        member2, created = TeamMemberTR.objects.get_or_create(
            full_name='New Member',
            defaults={
                'position': 'Developer',
                'email': '<EMAIL>'
            }
        )
        
        self.assertTrue(created)
        self.assertEqual(member2._state.db, 'turkish')

"""
Core tests for database routing functionality.

These tests verify that the database routing works correctly for Turkish and English models.
"""

from django.test import TestCase
from django.db import connections
from content.models import AboutUs
from content.models_tr import AboutUsTR


class DatabaseRoutingTest(TestCase):
    """Test database routing functionality."""
    databases = ['default', 'turkish']
    
    def test_english_model_uses_default_database(self):
        """Test that English models use the default database."""
        about = AboutUs.objects.create(
            title="Test Title",
            description="Test description",
            company_name="Test Company"
        )
        
        # Should be in default database
        self.assertEqual(about._state.db, 'default')
        
        # Verify it exists in default database
        self.assertTrue(AboutUs.objects.filter(id=about.id).exists())
    
    def test_turkish_model_uses_turkish_database(self):
        """Test that Turkish models use the Turkish database."""
        about_tr = AboutUsTR.objects.create(
            title="Test Başlık",
            description="Test açıklama",
            company_name="Test Şirket"
        )
        
        # Should be in Turkish database
        self.assertEqual(about_tr._state.db, 'turkish')
        
        # Verify it exists in Turkish database
        self.assertTrue(AboutUsTR.objects.filter(id=about_tr.id).exists())
    
    def test_data_isolation(self):
        """Test that Turkish and English data are completely isolated."""
        # Create English record
        about_en = AboutUs.objects.create(
            title="English Title",
            description="English description",
            company_name="English Company"
        )
        
        # Create Turkish record
        about_tr = AboutUsTR.objects.create(
            title="Türkçe Başlık",
            description="Türkçe açıklama",
            company_name="Türkçe Şirket"
        )
        
        # Verify counts
        self.assertEqual(AboutUs.objects.count(), 1)
        self.assertEqual(AboutUsTR.objects.count(), 1)
        
        # Verify they are different records
        self.assertNotEqual(about_en.id, about_tr.id)
        self.assertNotEqual(about_en.title, about_tr.title)
    
    def test_database_connections_exist(self):
        """Test that both database connections exist."""
        # Test default database connection
        default_conn = connections['default']
        self.assertIsNotNone(default_conn)
        
        # Test Turkish database connection
        turkish_conn = connections['turkish']
        self.assertIsNotNone(turkish_conn)
        
        # They should be different connections
        self.assertNotEqual(default_conn, turkish_conn)
    
    def test_queryset_database_routing(self):
        """Test that querysets use the correct database."""
        # English queryset should use default database
        en_queryset = AboutUs.objects.all()
        self.assertEqual(en_queryset.db, 'default')
        
        # Turkish queryset should use Turkish database
        tr_queryset = AboutUsTR.objects.all()
        self.assertEqual(tr_queryset.db, 'turkish')
    
    def test_model_creation_routing(self):
        """Test that model creation is routed to correct database."""
        # Create models and verify they end up in correct databases
        en_count_before = AboutUs.objects.count()
        tr_count_before = AboutUsTR.objects.count()
        
        # Create English model
        AboutUs.objects.create(
            title="New English",
            description="New English description",
            company_name="New English Company"
        )
        
        # Create Turkish model
        AboutUsTR.objects.create(
            title="Yeni Türkçe",
            description="Yeni Türkçe açıklama",
            company_name="Yeni Türkçe Şirket"
        )
        
        # Verify counts increased correctly
        self.assertEqual(AboutUs.objects.count(), en_count_before + 1)
        self.assertEqual(AboutUsTR.objects.count(), tr_count_before + 1)


class LanguageDetectionTest(TestCase):
    """Test language detection functionality."""
    
    def test_get_current_language_default(self):
        """Test that default language is English."""
        from core.routers import get_current_language
        
        # Should default to English
        current_lang = get_current_language()
        self.assertEqual(current_lang, 'en')
    
    def test_set_current_language(self):
        """Test setting current language."""
        from core.routers import get_current_language, set_current_language
        
        # Set to Turkish
        set_current_language('tr')
        self.assertEqual(get_current_language(), 'tr')
        
        # Set back to English
        set_current_language('en')
        self.assertEqual(get_current_language(), 'en')
    
    def test_clear_language_context(self):
        """Test clearing language context."""
        from core.routers import get_current_language, set_current_language, clear_language_context
        
        # Set language
        set_current_language('tr')
        self.assertEqual(get_current_language(), 'tr')
        
        # Clear context
        clear_language_context()
        
        # Should return to default
        self.assertEqual(get_current_language(), 'en')


class MiddlewareTest(TestCase):
    """Test language detection middleware."""
    
    def test_middleware_detects_language_from_url_param(self):
        """Test that middleware detects language from URL parameter."""
        # Test Turkish language parameter
        response = self.client.get('/?lang=tr')
        self.assertEqual(response.status_code, 200)
        
        # Test English language parameter
        response = self.client.get('/?lang=en')
        self.assertEqual(response.status_code, 200)
    
    def test_middleware_handles_invalid_language(self):
        """Test that middleware handles invalid language gracefully."""
        # Test invalid language parameter
        response = self.client.get('/?lang=invalid')
        self.assertEqual(response.status_code, 200)  # Should not crash
    
    def test_middleware_detects_language_from_header(self):
        """Test that middleware detects language from Accept-Language header."""
        # Test Turkish header
        response = self.client.get('/', HTTP_ACCEPT_LANGUAGE='tr')
        self.assertEqual(response.status_code, 200)
        
        # Test English header
        response = self.client.get('/', HTTP_ACCEPT_LANGUAGE='en-US,en;q=0.9')
        self.assertEqual(response.status_code, 200)


class ModelValidationTest(TestCase):
    """Test model validation and constraints."""
    databases = ['default', 'turkish']
    
    def test_turkish_model_validation(self):
        """Test that Turkish models validate correctly."""
        # Valid Turkish model
        about_tr = AboutUsTR(
            title="Geçerli Başlık",
            description="Geçerli açıklama",
            company_name="Geçerli Şirket",
            established_year=2020
        )
        
        # Should not raise validation error
        try:
            about_tr.full_clean()
        except Exception as e:
            self.fail(f"Valid Turkish model failed validation: {e}")
    
    def test_english_model_validation(self):
        """Test that English models validate correctly."""
        # Valid English model
        about_en = AboutUs(
            title="Valid Title",
            description="Valid description",
            company_name="Valid Company",
            established_year=2020
        )
        
        # Should not raise validation error
        try:
            about_en.full_clean()
        except Exception as e:
            self.fail(f"Valid English model failed validation: {e}")
    
    def test_model_string_representations(self):
        """Test model string representations."""
        # Create and test English model
        about_en = AboutUs.objects.create(
            title="English Title",
            description="English description",
            company_name="English Company"
        )
        expected_en = f"{about_en.title} - {about_en.company_name}"
        self.assertEqual(str(about_en), expected_en)
        
        # Create and test Turkish model
        about_tr = AboutUsTR.objects.create(
            title="Türkçe Başlık",
            description="Türkçe açıklama",
            company_name="Türkçe Şirket"
        )
        expected_tr = f"{about_tr.title} - {about_tr.company_name}"
        self.assertEqual(str(about_tr), expected_tr)


class DatabaseIntegrityTest(TestCase):
    """Test database integrity and constraints."""
    databases = ['default', 'turkish']
    
    def test_database_table_structure(self):
        """Test that database tables have correct structure."""
        # Test that we can create records in both databases
        try:
            # English database
            AboutUs.objects.create(
                title="Structure Test EN",
                description="Testing structure",
                company_name="Test Company EN"
            )
            
            # Turkish database
            AboutUsTR.objects.create(
                title="Structure Test TR",
                description="Testing structure",
                company_name="Test Company TR"
            )
            
        except Exception as e:
            self.fail(f"Database structure test failed: {e}")
    
    def test_database_isolation_integrity(self):
        """Test that databases maintain integrity isolation."""
        # Create records in both databases
        en_record = AboutUs.objects.create(
            title="Isolation Test EN",
            description="Testing isolation",
            company_name="Isolation Company EN"
        )
        
        tr_record = AboutUsTR.objects.create(
            title="Isolation Test TR",
            description="Testing isolation",
            company_name="Isolation Company TR"
        )
        
        # Delete English record
        en_record.delete()
        
        # Turkish record should still exist
        self.assertTrue(AboutUsTR.objects.filter(id=tr_record.id).exists())
        self.assertFalse(AboutUs.objects.filter(id=en_record.id).exists())
        
        # Counts should be correct
        self.assertEqual(AboutUs.objects.count(), 0)
        self.assertEqual(AboutUsTR.objects.count(), 1)

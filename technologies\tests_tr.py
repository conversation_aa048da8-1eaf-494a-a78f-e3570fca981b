"""
Unit tests for Turkish technology models.

These tests verify that Turkish technology models work correctly and use the Turkish database.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from .models_tr import TechnologyCategoryTR, TechnologyTR


class TechnologyCategoryTRModelTest(TestCase):
    """Test TechnologyCategoryTR model functionality."""
    databases = ['default', 'turkish']
    
    def setUp(self):
        """Set up test data."""
        self.category_data = {
            'name': 'web_development',
            'display_name': 'Web Geliştirme',
            'description': 'Web uygulaması geliştirme teknolojileri',
            'icon': 'fas fa-globe',
            'color_code': '#3498db'
        }
    
    def test_create_technology_category_tr(self):
        """Test creating TechnologyCategoryTR instance."""
        category = TechnologyCategoryTR.objects.create(**self.category_data)
        
        self.assertEqual(category.name, 'web_development')
        self.assertEqual(category.display_name, 'Web Geliştirme')
        self.assertEqual(category.description, 'Web uygulaması geliştirme teknolojileri')
        self.assertTrue(category.is_active)
        self.assertEqual(category.display_order, 0)
        self.assertIsNotNone(category.created_at)
        self.assertIsNotNone(category.updated_at)
    
    def test_technology_category_tr_uses_turkish_database(self):
        """Test that TechnologyCategoryTR uses Turkish database."""
        category = TechnologyCategoryTR.objects.create(**self.category_data)
        self.assertEqual(category._state.db, 'turkish')
    
    def test_technology_category_tr_str_representation(self):
        """Test string representation of TechnologyCategoryTR."""
        category = TechnologyCategoryTR.objects.create(**self.category_data)
        self.assertEqual(str(category), category.display_name)
    
    def test_technology_count_property(self):
        """Test technology_count property."""
        category = TechnologyCategoryTR.objects.create(**self.category_data)
        
        # Initially should be 0
        self.assertEqual(category.technology_count, 0)
        
        # Add some technologies
        TechnologyTR.objects.create(
            name='Django',
            category=category,
            proficiency_level='advanced'
        )
        TechnologyTR.objects.create(
            name='React',
            category=category,
            proficiency_level='intermediate'
        )
        
        # Should now be 2
        self.assertEqual(category.technology_count, 2)
    
    def test_featured_technology_count_property(self):
        """Test featured_technology_count property."""
        category = TechnologyCategoryTR.objects.create(**self.category_data)
        
        # Initially should be 0
        self.assertEqual(category.featured_technology_count, 0)
        
        # Add technologies, one featured
        TechnologyTR.objects.create(
            name='Django',
            category=category,
            proficiency_level='advanced',
            is_featured=True
        )
        TechnologyTR.objects.create(
            name='React',
            category=category,
            proficiency_level='intermediate',
            is_featured=False
        )
        
        # Should now be 1
        self.assertEqual(category.featured_technology_count, 1)


class TechnologyTRModelTest(TestCase):
    """Test TechnologyTR model functionality."""
    databases = ['default', 'turkish']
    
    def setUp(self):
        """Set up test data."""
        self.category = TechnologyCategoryTR.objects.create(
            name='web_development',
            display_name='Web Geliştirme'
        )
        
        self.technology_data = {
            'name': 'Django',
            'category': self.category,
            'description': 'Python web framework',
            'proficiency_level': 'advanced',
            'years_experience': 5,
            'logo_url': 'https://example.com/django-logo.png'
        }
    
    def test_create_technology_tr(self):
        """Test creating TechnologyTR instance."""
        tech = TechnologyTR.objects.create(**self.technology_data)
        
        self.assertEqual(tech.name, 'Django')
        self.assertEqual(tech.category, self.category)
        self.assertEqual(tech.proficiency_level, 'advanced')
        self.assertEqual(tech.years_experience, 5)
        self.assertFalse(tech.is_featured)
        self.assertTrue(tech.is_active)
        self.assertEqual(tech.display_order, 0)
    
    def test_technology_tr_uses_turkish_database(self):
        """Test that TechnologyTR uses Turkish database."""
        tech = TechnologyTR.objects.create(**self.technology_data)
        self.assertEqual(tech._state.db, 'turkish')
    
    def test_technology_tr_str_representation(self):
        """Test string representation of TechnologyTR."""
        tech = TechnologyTR.objects.create(**self.technology_data)
        expected_str = f"{tech.name} ({tech.category.display_name})"
        self.assertEqual(str(tech), expected_str)
    
    def test_proficiency_percentage_property(self):
        """Test proficiency_percentage property."""
        test_cases = [
            ('beginner', 25),
            ('intermediate', 50),
            ('advanced', 75),
            ('expert', 95)
        ]
        
        for proficiency, expected_percentage in test_cases:
            tech_data = self.technology_data.copy()
            tech_data['proficiency_level'] = proficiency
            tech_data['name'] = f'Test-{proficiency}'
            tech = TechnologyTR.objects.create(**tech_data)
            
            self.assertEqual(tech.proficiency_percentage, expected_percentage)
            
            # Clean up for next iteration
            tech.delete()
    
    def test_experience_level_property(self):
        """Test experience_level property."""
        test_cases = [
            (None, 'Belirtilmemiş'),
            (0, 'Yeni'),
            (1, '1 yıl'),
            (3, '3 yıl'),
            (7, '7 yıl (Deneyimli)'),
            (12, '12 yıl (Uzman)')
        ]
        
        for years, expected_level in test_cases:
            tech_data = self.technology_data.copy()
            tech_data['years_experience'] = years
            tech_data['name'] = f'Test-{years or 0}'
            tech = TechnologyTR.objects.create(**tech_data)
            
            self.assertEqual(tech.experience_level, expected_level)
            
            # Clean up for next iteration
            tech.delete()
    
    def test_proficiency_color_property(self):
        """Test proficiency_color property."""
        test_cases = [
            ('beginner', '#ff6b6b'),
            ('intermediate', '#feca57'),
            ('advanced', '#48dbfb'),
            ('expert', '#0be881')
        ]
        
        for proficiency, expected_color in test_cases:
            tech_data = self.technology_data.copy()
            tech_data['proficiency_level'] = proficiency
            tech_data['name'] = f'Color-{proficiency}'
            tech = TechnologyTR.objects.create(**tech_data)
            
            self.assertEqual(tech.proficiency_color, expected_color)
            
            # Clean up for next iteration
            tech.delete()
    
    def test_proficiency_icon_property(self):
        """Test proficiency_icon property."""
        test_cases = [
            ('beginner', 'fas fa-seedling'),
            ('intermediate', 'fas fa-chart-line'),
            ('advanced', 'fas fa-trophy'),
            ('expert', 'fas fa-crown')
        ]
        
        for proficiency, expected_icon in test_cases:
            tech_data = self.technology_data.copy()
            tech_data['proficiency_level'] = proficiency
            tech_data['name'] = f'Icon-{proficiency}'
            tech = TechnologyTR.objects.create(**tech_data)
            
            self.assertEqual(tech.proficiency_icon, expected_icon)
            
            # Clean up for next iteration
            tech.delete()
    
    def test_technology_validation(self):
        """Test TechnologyTR validation."""
        # Test that years_experience should match proficiency_level
        invalid_data = self.technology_data.copy()
        invalid_data['proficiency_level'] = 'expert'
        invalid_data['years_experience'] = 1  # Too low for expert level
        
        tech = TechnologyTR(**invalid_data)
        with self.assertRaises(ValidationError):
            tech.full_clean()
    
    def test_get_featured_technologies_class_method(self):
        """Test get_featured_technologies class method."""
        # Create featured and non-featured technologies
        featured_tech = TechnologyTR.objects.create(
            name='Featured Tech',
            category=self.category,
            is_featured=True
        )
        
        non_featured_tech = TechnologyTR.objects.create(
            name='Non-Featured Tech',
            category=self.category,
            is_featured=False
        )
        
        featured_technologies = TechnologyTR.get_featured_technologies()
        
        self.assertIn(featured_tech, featured_technologies)
        self.assertNotIn(non_featured_tech, featured_technologies)
    
    def test_get_featured_technologies_with_limit(self):
        """Test get_featured_technologies with limit."""
        # Create multiple featured technologies
        for i in range(5):
            TechnologyTR.objects.create(
                name=f'Featured Tech {i}',
                category=self.category,
                is_featured=True,
                display_order=i
            )
        
        featured_technologies = TechnologyTR.get_featured_technologies(limit=3)
        self.assertEqual(len(featured_technologies), 3)
    
    def test_get_by_category_class_method(self):
        """Test get_by_category class method."""
        # Create another category
        mobile_category = TechnologyCategoryTR.objects.create(
            name='mobile_development',
            display_name='Mobil Geliştirme'
        )
        
        # Create technologies in different categories
        web_tech = TechnologyTR.objects.create(
            name='Django',
            category=self.category
        )
        
        mobile_tech = TechnologyTR.objects.create(
            name='Flutter',
            category=mobile_category
        )
        
        # Test getting all categories
        all_categories = TechnologyTR.get_by_category()
        self.assertIn(self.category, all_categories)
        self.assertIn(mobile_category, all_categories)
        self.assertIn(web_tech, all_categories[self.category])
        self.assertIn(mobile_tech, all_categories[mobile_category])
        
        # Test getting specific category
        web_only = TechnologyTR.get_by_category('web_development')
        self.assertIn(self.category, web_only)
        self.assertNotIn(mobile_category, web_only)
    
    def test_get_proficiency_stats_class_method(self):
        """Test get_proficiency_stats class method."""
        # Create technologies with different proficiency levels
        TechnologyTR.objects.create(
            name='Beginner Tech',
            category=self.category,
            proficiency_level='beginner'
        )
        
        TechnologyTR.objects.create(
            name='Advanced Tech 1',
            category=self.category,
            proficiency_level='advanced'
        )
        
        TechnologyTR.objects.create(
            name='Advanced Tech 2',
            category=self.category,
            proficiency_level='advanced'
        )
        
        stats = TechnologyTR.get_proficiency_stats()
        
        # Should have stats for beginner (1) and advanced (2)
        beginner_stat = next((s for s in stats if s['proficiency_level'] == 'beginner'), None)
        advanced_stat = next((s for s in stats if s['proficiency_level'] == 'advanced'), None)
        
        self.assertIsNotNone(beginner_stat)
        self.assertIsNotNone(advanced_stat)
        self.assertEqual(beginner_stat['count'], 1)
        self.assertEqual(advanced_stat['count'], 2)
    
    def test_unique_together_constraint(self):
        """Test unique_together constraint for name and category."""
        # Create first technology
        TechnologyTR.objects.create(**self.technology_data)
        
        # Try to create another with same name and category
        duplicate_data = self.technology_data.copy()
        with self.assertRaises(Exception):  # Should raise IntegrityError
            TechnologyTR.objects.create(**duplicate_data)

"""
Unit tests for Turkish content models.

These tests verify that Turkish models work correctly and use the Turkish database.
"""

from django.test import TestCase
from django.db import connections
from django.core.exceptions import ValidationError
from .models_tr import AboutUsTR, ServiceTR, AdditionalServiceTR, FeaturedResourceTR, SocialMediaLinkTR
from datetime import date


class TurkishModelDatabaseRoutingTest(TestCase):
    """Test that Turkish models use the Turkish database."""
    databases = ['default', 'turkish']
    
    def test_turkish_models_use_turkish_database(self):
        """Verify that Turkish models are routed to the Turkish database."""
        # Create instances of Turkish models
        about = AboutUsTR.objects.create(
            title="Test Başlık",
            description="Test açıklama",
            company_name="Bean Software"
        )
        
        service = ServiceTR.objects.create(
            title="Test Hizmet",
            category="web_development",
            short_description="Test hizmet açıklaması"
        )
        
        # Check that they were created in the Turkish database
        self.assertEqual(about._state.db, 'turkish')
        self.assertEqual(service._state.db, 'turkish')
        
        # Verify they exist in Turkish database but not in default
        turkish_connection = connections['turkish']
        default_connection = connections['default']
        
        with turkish_connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM about_us_tr")
            turkish_count = cursor.fetchone()[0]
            self.assertEqual(turkish_count, 1)
        
        with default_connection.cursor() as cursor:
            # This table shouldn't exist in default database
            try:
                cursor.execute("SELECT COUNT(*) FROM about_us_tr")
                default_count = cursor.fetchone()[0]
                self.assertEqual(default_count, 0)  # Should be 0 if table exists
            except Exception:
                # Table doesn't exist in default database - this is expected
                pass


class AboutUsTRModelTest(TestCase):
    """Test AboutUsTR model functionality."""
    databases = ['default', 'turkish']
    
    def setUp(self):
        """Set up test data."""
        self.about_data = {
            'title': 'Bean Software Hakkında',
            'description': 'Yazılım geliştirme şirketi',
            'company_name': 'Bean Software',
            'established_year': 2020,
            'mission_statement': 'Kaliteli yazılım çözümleri sunmak',
            'vision_statement': 'Teknoloji lideri olmak',
            'values': 'Kalite, İnovasyon, Güvenilirlik'
        }
    
    def test_create_about_us_tr(self):
        """Test creating AboutUsTR instance."""
        about = AboutUsTR.objects.create(**self.about_data)
        
        self.assertEqual(about.title, 'Bean Software Hakkında')
        self.assertEqual(about.company_name, 'Bean Software')
        self.assertEqual(about.established_year, 2020)
        self.assertTrue(about.is_active)
        self.assertEqual(about.display_order, 0)
        self.assertIsNotNone(about.created_at)
        self.assertIsNotNone(about.updated_at)
    
    def test_about_us_tr_str_representation(self):
        """Test string representation of AboutUsTR."""
        about = AboutUsTR.objects.create(**self.about_data)
        expected_str = f"{about.title} - {about.company_name}"
        self.assertEqual(str(about), expected_str)
    
    def test_about_us_tr_validation(self):
        """Test validation of AboutUsTR fields."""
        # Test invalid established year
        invalid_data = self.about_data.copy()
        invalid_data['established_year'] = 1800  # Too old
        
        about = AboutUsTR(**invalid_data)
        with self.assertRaises(ValidationError):
            about.full_clean()


class ServiceTRModelTest(TestCase):
    """Test ServiceTR model functionality."""
    databases = ['default', 'turkish']
    
    def setUp(self):
        """Set up test data."""
        self.service_data = {
            'title': 'Web Geliştirme',
            'category': 'web_development',
            'short_description': 'Modern web uygulamaları geliştiriyoruz',
            'full_description': 'React, Django ve diğer modern teknolojilerle web uygulamaları',
            'price_range': '10.000 - 50.000 TL',
            'delivery_time': '2-4 hafta'
        }
    
    def test_create_service_tr(self):
        """Test creating ServiceTR instance."""
        service = ServiceTR.objects.create(**self.service_data)
        
        self.assertEqual(service.title, 'Web Geliştirme')
        self.assertEqual(service.category, 'web_development')
        self.assertFalse(service.is_featured)
        self.assertTrue(service.is_active)
        self.assertEqual(service.display_order, 0)
    
    def test_service_tr_str_representation(self):
        """Test string representation of ServiceTR."""
        service = ServiceTR.objects.create(**self.service_data)
        expected_str = f"{service.title} ({service.get_category_display()})"
        self.assertEqual(str(service), expected_str)
    
    def test_service_tr_features_list_property(self):
        """Test features_list property."""
        service = ServiceTR.objects.create(**self.service_data)
        
        # Test with no features
        self.assertEqual(service.features_list, [])
        
        # Test with JSON features
        service.features = ['React', 'Django', 'PostgreSQL']
        service.save()
        self.assertEqual(service.features_list, ['React', 'Django', 'PostgreSQL'])


class AdditionalServiceTRModelTest(TestCase):
    """Test AdditionalServiceTR model functionality."""
    databases = ['default', 'turkish']
    
    def setUp(self):
        """Set up test data."""
        self.additional_service_data = {
            'title': 'SEO Optimizasyonu',
            'subtitle': 'Arama motoru optimizasyonu',
            'description': 'Web sitenizin arama motorlarında üst sıralarda yer almasını sağlıyoruz',
            'price_info': '5.000 TL/ay'
        }
    
    def test_create_additional_service_tr(self):
        """Test creating AdditionalServiceTR instance."""
        service = AdditionalServiceTR.objects.create(**self.additional_service_data)
        
        self.assertEqual(service.title, 'SEO Optimizasyonu')
        self.assertEqual(service.subtitle, 'Arama motoru optimizasyonu')
        self.assertFalse(service.is_popular)
        self.assertTrue(service.is_active)
    
    def test_additional_service_tr_str_representation(self):
        """Test string representation of AdditionalServiceTR."""
        service = AdditionalServiceTR.objects.create(**self.additional_service_data)
        self.assertEqual(str(service), service.title)


class FeaturedResourceTRModelTest(TestCase):
    """Test FeaturedResourceTR model functionality."""
    databases = ['default', 'turkish']
    
    def setUp(self):
        """Set up test data."""
        self.resource_data = {
            'title': 'Django ile Web Geliştirme',
            'description': 'Django framework kullanarak web uygulaması geliştirme rehberi',
            'image_url': 'https://example.com/django-guide.jpg',
            'resource_type': 'blog_post',
            'content_url': 'https://example.com/django-guide',
            'reading_time': 15,
            'difficulty_level': 'intermediate',
            'author': 'Ahmet Yılmaz',
            'published_date': date.today()
        }
    
    def test_create_featured_resource_tr(self):
        """Test creating FeaturedResourceTR instance."""
        resource = FeaturedResourceTR.objects.create(**self.resource_data)
        
        self.assertEqual(resource.title, 'Django ile Web Geliştirme')
        self.assertEqual(resource.resource_type, 'blog_post')
        self.assertEqual(resource.reading_time, 15)
        self.assertTrue(resource.is_featured)
        self.assertTrue(resource.is_active)
        self.assertEqual(resource.view_count, 0)
    
    def test_featured_resource_tr_str_representation(self):
        """Test string representation of FeaturedResourceTR."""
        resource = FeaturedResourceTR.objects.create(**self.resource_data)
        expected_str = f"{resource.title} ({resource.get_resource_type_display()})"
        self.assertEqual(str(resource), expected_str)
    
    def test_increment_view_count(self):
        """Test incrementing view count."""
        resource = FeaturedResourceTR.objects.create(**self.resource_data)
        initial_count = resource.view_count
        
        resource.increment_view_count()
        resource.refresh_from_db()
        
        self.assertEqual(resource.view_count, initial_count + 1)


class SocialMediaLinkTRModelTest(TestCase):
    """Test SocialMediaLinkTR model functionality."""
    databases = ['default', 'turkish']
    
    def setUp(self):
        """Set up test data."""
        self.social_data = {
            'platform': 'linkedin',
            'display_name': 'Bean Software LinkedIn',
            'url': 'https://linkedin.com/company/bean-software',
            'follower_count': 1500
        }
    
    def test_create_social_media_link_tr(self):
        """Test creating SocialMediaLinkTR instance."""
        social = SocialMediaLinkTR.objects.create(**self.social_data)
        
        self.assertEqual(social.platform, 'linkedin')
        self.assertEqual(social.display_name, 'Bean Software LinkedIn')
        self.assertEqual(social.follower_count, 1500)
        self.assertTrue(social.is_active)
    
    def test_social_media_link_tr_str_representation(self):
        """Test string representation of SocialMediaLinkTR."""
        social = SocialMediaLinkTR.objects.create(**self.social_data)
        expected_str = f"{social.get_platform_display()} - {social.display_name}"
        self.assertEqual(str(social), expected_str)
    
    def test_formatted_follower_count(self):
        """Test formatted follower count property."""
        # Test with different follower counts
        test_cases = [
            (1500, '1.5K'),
            (1000000, '1.0M'),
            (500, '500'),
            (None, None)
        ]
        
        for count, expected in test_cases:
            social_data = self.social_data.copy()
            social_data['follower_count'] = count
            social = SocialMediaLinkTR.objects.create(**social_data)
            
            self.assertEqual(social.formatted_follower_count, expected)
            
            # Clean up for next iteration
            social.delete()


class TurkishModelManagerTest(TestCase):
    """Test TurkishModelManager functionality."""
    databases = ['default', 'turkish']
    
    def test_turkish_manager_uses_turkish_database(self):
        """Test that TurkishModelManager uses Turkish database."""
        # Create an instance using the manager
        about = AboutUsTR.objects.create(
            title="Test",
            description="Test",
            company_name="Test"
        )
        
        # Verify it's in the Turkish database
        self.assertEqual(about._state.db, 'turkish')
        
        # Test queryset uses Turkish database
        queryset = AboutUsTR.objects.all()
        self.assertEqual(queryset.db, 'turkish')
    
    def test_turkish_manager_get_or_create(self):
        """Test get_or_create method uses Turkish database."""
        about, created = AboutUsTR.objects.get_or_create(
            title="Test Get or Create",
            defaults={
                'description': 'Test description',
                'company_name': 'Test Company'
            }
        )
        
        self.assertTrue(created)
        self.assertEqual(about._state.db, 'turkish')
        
        # Test getting existing object
        about2, created2 = AboutUsTR.objects.get_or_create(
            title="Test Get or Create",
            defaults={
                'description': 'Different description',
                'company_name': 'Different Company'
            }
        )
        
        self.assertFalse(created2)
        self.assertEqual(about.id, about2.id)
